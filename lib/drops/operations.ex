defmodule Drops.Operations do
  @moduledoc """
  Operations module for defining command and query operations.

  This module provides a framework for defining operations that can be used
  to encapsulate business logic with input validation and execution.
  """

  defmodule Success do
    @type t :: %__MODULE__{}

    defstruct [:type, :operation, :result, :params]
  end

  defmodule Failure do
    @type t :: %__MODULE__{}

    defstruct [:type, :operation, :result, :params]
  end

  @doc """
  Callback for executing an operation with given parameters.
  """
  @callback execute(params :: any()) :: {:ok, any()} | {:error, any()}

  @doc """
  Callback for preparing parameters before execution.
  """
  @callback prepare(params :: any()) :: any()

  @callback validate(params :: any()) :: {:ok, any()} | {:error, any()}

  defmacro __using__(_opts) do
    quote do
      import Drops.Operations

      defmacro __using__(type) when is_atom(type) do
        Drops.Operations.__define_operation__(type: type)
      end

      defmacro __using__(opts) when is_list(opts) do
        unless Keyword.has_key?(opts, :type) do
          raise ArgumentError, "type option is required when using Drops.Operations"
        end

        Drops.Operations.__define_operation__(opts)
      end
    end
  end

  @doc false
  def __define_operation__(opts) do
    quote do
      @behaviour Drops.Operations

      use Drops.Contract
      import Ecto.Changeset

      # Store the repo configuration if provided
      @repo unquote(opts[:repo])

      # Store the operation type
      @operation_type unquote(opts[:type])

      # Set default schema options based on operation type
      @schema_opts (case unquote(opts[:type]) do
                      :form -> [atomize: true]
                      _ -> []
                    end)

      schema do
        %{}
      end

      def call(params) do
        case __validate__(params, @operation_type) do
          {:ok, validated_params} ->
            case execute(validated_params) do
              {:ok, result} ->
                {:ok,
                 %Drops.Operations.Success{
                   operation: __MODULE__,
                   result: result,
                   params: validated_params,
                   type: @operation_type
                 }}

              {:error, error} ->
                {:error,
                 %Drops.Operations.Failure{
                   operation: __MODULE__,
                   result: error,
                   params: validated_params,
                   type: @operation_type
                 }}
            end

          {:error, errors} ->
            {:error,
             %Drops.Operations.Failure{
               operation: __MODULE__,
               result: errors,
               params: params,
               type: @operation_type
             }}
        end
      end

      def __validate__(params, operation_type) do
        cond do
          length(schema().keys) == 0 ->
            prepared_params = prepare(params)
            {:ok, prepared_params}

          is_nil(schema().meta[:source_schema]) ->
            case conform(params) do
              {:ok, conformed_params} ->
                prepared_params = prepare(conformed_params)
                {:ok, prepared_params}

              {:error, errors} ->
                {:error, errors}
            end

          true ->
            case conform(params) do
              {:ok, conformed_params} ->
                prepared_params = prepare(conformed_params)

                changeset =
                  prepared_params
                  |> changeset()
                  |> validate()

                if changeset.valid? do
                  {:ok, prepared_params}
                else
                  {:error, prepare_changeset_error(changeset, operation_type)}
                end

              {:error, errors} ->
                handle_schema_validation_errors(errors, operation_type)
            end
        end
      end

      # Prepare changeset error based on operation type
      defp prepare_changeset_error(changeset, :form) do
        Map.put(changeset, :action, :validate)
      end

      defp prepare_changeset_error(changeset, _operation_type) do
        changeset
      end

      # Handle schema validation errors based on operation type
      defp handle_schema_validation_errors(errors, :form) do
        case schema().meta[:source_schema] do
          nil ->
            # No Ecto schema, just return the errors
            {:error, errors}

          _source_schema ->
            # Convert schema errors to changeset for FormData compatibility
            # Use empty params for changeset since validation failed
            changeset =
              %{}
              |> changeset()
              |> convert_schema_errors_to_changeset(errors)
              |> Map.put(:action, :validate)

            {:error, changeset}
        end
      end

      defp handle_schema_validation_errors(errors, _operation_type) do
        {:error, errors}
      end

      def execute(params) do
        raise "execute/1 must be implemented"
      end

      def prepare(params) do
        params
      end

      def validate(changeset) do
        changeset
      end

      def changeset(params) do
        source_schema = schema().meta[:source_schema]

        Ecto.Changeset.change(struct(source_schema), params)
      end

      # Helper function to convert schema validation errors to changeset errors
      defp convert_schema_errors_to_changeset(changeset, schema_errors) do
        Enum.reduce(schema_errors, changeset, fn error, acc ->
          case error do
            %{path: [field], text: text} when is_atom(field) ->
              Ecto.Changeset.add_error(acc, field, text)

            %{path: [field], text: text} when is_binary(field) ->
              field_atom = String.to_existing_atom(field)
              Ecto.Changeset.add_error(acc, field_atom, text)

            # Handle nested paths by flattening to the first level for now
            %{path: [field | _], text: text} when is_atom(field) ->
              Ecto.Changeset.add_error(acc, field, text)

            %{path: [field | _], text: text} when is_binary(field) ->
              field_atom = String.to_existing_atom(field)
              Ecto.Changeset.add_error(acc, field_atom, text)

            # Fallback for other error structures
            _ ->
              acc
          end
        end)
      end

      if unquote(opts[:repo]) do
        def persist(params) do
          @repo.insert(changeset(params))
        end
      end

      defoverridable execute: 1
      defoverridable prepare: 1
      defoverridable validate: 1
    end
  end

  # Phoenix.HTML.FormData protocol implementations for form compatibility
  if Code.ensure_loaded?(Phoenix.HTML.FormData) do
    defimpl Phoenix.HTML.FormData, for: Drops.Operations.Success do
      def to_form(%{params: params, type: :form}, options) do
        # For :form operations, use the validated params as the form data
        # This allows the Success struct to work with Phoenix form helpers
        # Convert atom keys to string keys as required by Phoenix.HTML
        form_data = if is_map(params), do: stringify_keys(params), else: %{}
        create_form_struct(form_data, options, "success")
      end

      def to_form(%{params: params}, options) do
        # For non-form operations, fall back to params
        # Convert atom keys to string keys as required by Phoenix.HTML
        form_data = if is_map(params), do: stringify_keys(params), else: %{}
        create_form_struct(form_data, options, "success")
      end

      def to_form(data, form, field, options) do
        form_data = if is_map(data.params), do: stringify_keys(data.params), else: %{}
        Phoenix.HTML.FormData.to_form(form_data, form, field, options)
      end

      def input_value(%{params: params}, form, field) do
        form_data = if is_map(params), do: stringify_keys(params), else: %{}
        Phoenix.HTML.FormData.input_value(form_data, form, field)
      end

      def input_validations(%{params: _params}, _form, _field) do
        []
      end

      # Helper function to create a proper Phoenix.HTML.Form struct
      defp create_form_struct(form_data, options, default_name) do
        {name, options} = Keyword.pop(options, :as)
        name = to_string(name || default_name)
        id = Keyword.get(options, :id) || name

        %Phoenix.HTML.Form{
          source: form_data,
          impl: __MODULE__,
          id: id,
          name: name,
          data: form_data,
          params: form_data,
          errors: [],
          hidden: [],
          options: options,
          action: nil,
          index: nil
        }
      end

      # Helper function to convert atom keys to string keys
      defp stringify_keys(map) when is_map(map) do
        Map.new(map, fn
          {key, value} when is_atom(key) -> {Atom.to_string(key), value}
          {key, value} -> {key, value}
        end)
      end

      defp stringify_keys(other), do: other
    end

    defimpl Phoenix.HTML.FormData, for: Drops.Operations.Failure do
      def to_form(%{params: params, result: result, type: :form}, options) do
        # For :form operations with validation errors, we want to preserve
        # the original params and include error information
        # Convert atom keys to string keys as required by Phoenix.HTML
        form_data = if is_map(params), do: stringify_keys(params), else: %{}

        # If result is an Ecto.Changeset, use it directly for form data
        # as it contains both data and errors
        case result do
          %Ecto.Changeset{} = changeset ->
            Phoenix.HTML.FormData.to_form(changeset, options)

          _ ->
            create_form_struct(form_data, options, "failure")
        end
      end

      def to_form(%{params: params, result: result}, options) do
        # For non-form operations, check if result is a changeset
        case result do
          %Ecto.Changeset{} = changeset ->
            Phoenix.HTML.FormData.to_form(changeset, options)

          _ ->
            form_data = if is_map(params), do: stringify_keys(params), else: %{}
            create_form_struct(form_data, options, "failure")
        end
      end

      def to_form(data, form, field, options) do
        case data.result do
          %Ecto.Changeset{} = changeset ->
            Phoenix.HTML.FormData.to_form(changeset, form, field, options)

          _ ->
            form_data = if is_map(data.params), do: stringify_keys(data.params), else: %{}
            Phoenix.HTML.FormData.to_form(form_data, form, field, options)
        end
      end

      def input_value(%{params: params, result: result}, form, field) do
        case result do
          %Ecto.Changeset{} = changeset ->
            Phoenix.HTML.FormData.input_value(changeset, form, field)

          _ ->
            form_data = if is_map(params), do: stringify_keys(params), else: %{}
            Phoenix.HTML.FormData.input_value(form_data, form, field)
        end
      end

      def input_validations(%{params: _params, result: result}, form, field) do
        case result do
          %Ecto.Changeset{} = changeset ->
            Phoenix.HTML.FormData.input_validations(changeset, form, field)

          _ ->
            []
        end
      end

      # Helper function to create a proper Phoenix.HTML.Form struct
      defp create_form_struct(form_data, options, default_name) do
        {name, options} = Keyword.pop(options, :as)
        name = to_string(name || default_name)
        id = Keyword.get(options, :id) || name

        %Phoenix.HTML.Form{
          source: form_data,
          impl: __MODULE__,
          id: id,
          name: name,
          data: form_data,
          params: form_data,
          errors: [],
          hidden: [],
          options: options,
          action: nil,
          index: nil
        }
      end

      # Helper function to convert atom keys to string keys
      defp stringify_keys(map) when is_map(map) do
        Map.new(map, fn
          {key, value} when is_atom(key) -> {Atom.to_string(key), value}
          {key, value} -> {key, value}
        end)
      end

      defp stringify_keys(other), do: other
    end
  end
end
